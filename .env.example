# CodeReview CLI 环境变量配置示例
# 复制此文件为 .env 并填入实际值
# 标注说明：[必填] = 必须配置，[选填] = 可选配置

# ==================== GitLab 配置 ====================

# [必填] GitLab Personal Access Token
# 在 GitLab 中生成：Settings > Access Tokens > Personal Access Tokens
# 需要的权限：api, read_repository, write_repository
GITLAB_PERSONAL_ACCESS_TOKEN=your_gitlab_token_here

# [选填] GitLab API URL（默认为 https://gitlab.com/api/v4）
# 如果使用私有 GitLab 实例，请修改此 URL
GITLAB_API_URL=https://gitlab.com/api/v4

# ==================== AI 服务配置 ====================

# [选填] 选择使用的AI服务（默认: gemini）
# 支持的服务：gemini, opencode, claudecode
AI_SERVICE=gemini

# [选填] 代码审查时机（默认: post-commit）
# 支持的时机：pre-commit（提交前审查）, post-commit（提交后审查）
REVIEW_TIMING=post-commit

# [选填] AI服务调用超时时间，单位：秒（默认: 30）
AI_TIMEOUT=30

# [选填] AI服务重试次数（默认: 3）
AI_MAX_RETRIES=3

# ==================== Gemini 配置 ====================

# [必填] Gemini API Key（如果使用Gemini服务）
# 获取地址：https://aistudio.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# [选填] Gemini 模型（默认: gemini-pro）
GEMINI_MODEL=gemini-pro

# ==================== OpenCode 配置 ====================

# [必填] OpenCode API Key（如果使用OpenCode服务）
OPENCODE_API_KEY=your_opencode_api_key_here

# [选填] OpenCode API URL（默认: https://api.opencode.com/v1）
OPENCODE_API_URL=https://api.opencode.com/v1

# [选填] OpenCode 模型（默认: opencode-pro）
OPENCODE_MODEL=opencode-pro

# ==================== ClaudeCode 配置 ====================

# [必填] ClaudeCode API Key（如果使用ClaudeCode服务）
CLAUDECODE_API_KEY=your_claudecode_api_key_here

# [选填] ClaudeCode API URL（默认: https://api.claudecode.com/v1）
CLAUDECODE_API_URL=https://api.claudecode.com/v1

# [选填] ClaudeCode 模型（默认: claude-3-sonnet）
CLAUDECODE_MODEL=claude-3-sonnet

# ==================== 自动更新配置 ====================

# [选填] 启用自动更新（默认: true）
# 每天首次使用时自动检查并安装更新
AUTO_UPDATE_ENABLED=true

# [选填] 更新检查间隔（默认: daily）
# 支持的值：daily（每天）, weekly（每周）, never（从不）
UPDATE_CHECK_INTERVAL=daily

# [选填] 更新渠道（默认: stable）
# 支持的值：stable（稳定版）, beta（测试版）
UPDATE_CHANNEL=stable

# ==================== 其他配置 ====================

# [选填] 审查报告输出目录（默认: ./review_logs）
REVIEW_LOGS_DIR=./review_logs

# [选填] 启用调试模式（默认: false）
DEBUG=false

# ==================== 配置示例 ====================

# 示例1：使用Gemini服务
# AI_SERVICE=gemini
# GEMINI_API_KEY=your_actual_gemini_key
# GITLAB_PERSONAL_ACCESS_TOKEN=glpat-xxxxxxxxxxxxxxxxxxxx

# 示例2：使用OpenCode服务
# AI_SERVICE=opencode
# OPENCODE_API_KEY=your_actual_opencode_key
# GITLAB_PERSONAL_ACCESS_TOKEN=glpat-xxxxxxxxxxxxxxxxxxxx

# 示例3：使用私有GitLab实例
# GITLAB_API_URL=https://gitlab.yeepay.com/api/v4
# GITLAB_PERSONAL_ACCESS_TOKEN=glpat-xxxxxxxxxxxxxxxxxxxx

# AI 服务配置
# 选择使用的AI服务：gemini, opencode, claudecode
AI_SERVICE=gemini

# Gemini 配置
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro

# OpenCode 配置
OPENCODE_API_KEY=your_opencode_api_key_here
OPENCODE_API_URL=https://api.opencode.com/v1
OPENCODE_MODEL=opencode-pro

# ClaudeCode 配置
CLAUDECODE_API_KEY=your_claudecode_api_key_here
CLAUDECODE_API_URL=https://api.claudecode.com/v1
CLAUDECODE_MODEL=claude-3-sonnet

# AI 服务通用配置
AI_TIMEOUT=30
AI_MAX_RETRIES=3

# 示例：
# GITLAB_PERSONAL_ACCESS_TOKEN=glpat-xxxxxxxxxxxxxxxxxxxx
# GITLAB_API_URL=https://gitlab.yeepay.com/api/v4
# AI_SERVICE=gemini
# GEMINI_API_KEY=your_actual_gemini_key
