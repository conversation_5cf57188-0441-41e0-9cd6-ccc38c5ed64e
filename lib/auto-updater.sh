#!/bin/bash

# Auto Updater Module
# 自动版本检查和升级模块

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置常量
GITHUB_API_URL="https://api.github.com/repos/im47cn/codereview-cli/releases/latest"
UPDATE_TIMEOUT=5
CACHE_DIR="$HOME/.codereview-cli"
CACHE_FILE="$CACHE_DIR/update_cache"
LOCK_FILE="$CACHE_DIR/update.lock"
LOG_FILE="$CACHE_DIR/update.log"
BACKUP_DIR="$CACHE_DIR/backup"

# 导入依赖模块
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/version.sh"
source "$SCRIPT_DIR/ai-config.sh"

# 确保缓存目录存在
#
# 功能: 创建必要的目录结构
# 参数: 无
# 返回: 无
# 复杂度: O(1) - 常数时间操作
ensure_cache_dir() {
    mkdir -p "$CACHE_DIR" "$BACKUP_DIR" 2>/dev/null
}

# 记录日志
#
# 功能: 记录更新相关的日志信息
# 参数:
#   $1 - level: 日志级别 (INFO/WARN/ERROR)
#   $2 - message: 日志消息
# 返回: 无
# 复杂度: O(1) - 常数时间写入
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    ensure_cache_dir
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    
    # 调试模式下也输出到控制台
    if [ "$DEBUG" = "true" ]; then
        echo -e "${BLUE}[AutoUpdater]${NC} [$level] $message" >&2
    fi
}

# 检查是否启用自动更新
#
# 功能: 检查用户配置是否启用自动更新功能
# 参数: 无
# 返回: 0=启用, 1=禁用
# 复杂度: O(1) - 配置查找
# 依赖: get_config_value()
is_auto_update_enabled() {
    local enabled=$(get_config_value "AUTO_UPDATE_ENABLED" "global")
    
    # 默认启用自动更新
    if [ -z "$enabled" ]; then
        enabled="true"
    fi
    
    case "$enabled" in
        "true"|"1"|"yes"|"on")
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# 检查今天是否已经检查过更新
#
# 功能: 避免同一天重复检查更新
# 参数: 无
# 返回: 0=今天已检查, 1=今天未检查
# 复杂度: O(1) - 文件读取
should_check_today() {
    if [ ! -f "$CACHE_FILE" ]; then
        return 1  # 缓存文件不存在，需要检查
    fi
    
    local last_check_date=$(grep "^last_check_date=" "$CACHE_FILE" 2>/dev/null | cut -d'=' -f2)
    local today=$(date '+%Y-%m-%d')
    
    if [ "$last_check_date" = "$today" ]; then
        return 0  # 今天已经检查过
    else
        return 1  # 今天还没检查过
    fi
}

# 获取远程最新版本
#
# 功能: 从 GitHub API 获取最新版本号
# 参数: 无
# 返回: 版本号字符串，失败时返回空
# 复杂度: O(1) - 网络请求
# 依赖: curl, jq (可选)
get_latest_version() {
    local latest_version=""
    
    # 使用 curl 获取最新版本信息
    local response=$(curl -s --connect-timeout $UPDATE_TIMEOUT "$GITHUB_API_URL" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ ! -z "$response" ]; then
        # 尝试使用 jq 解析 JSON
        if command -v jq &> /dev/null; then
            latest_version=$(echo "$response" | jq -r '.tag_name' 2>/dev/null)
        else
            # 备选方案：使用 grep 和 sed 解析
            latest_version=$(echo "$response" | grep '"tag_name"' | head -1 | sed 's/.*"tag_name": *"\([^"]*\)".*/\1/')
        fi
        
        # 清理版本号（移除 v 前缀）
        latest_version=$(echo "$latest_version" | sed 's/^v//')
    fi
    
    echo "$latest_version"
}

# 比较版本号
#
# 功能: 比较两个版本号的大小
# 参数:
#   $1 - version1: 第一个版本号
#   $2 - version2: 第二个版本号
# 返回: 0=version1>=version2, 1=version1<version2
# 复杂度: O(n) - n为版本号段数
# 格式: 支持 x.y.z 格式的版本号
compare_versions() {
    local version1="$1"
    local version2="$2"
    
    # 处理空版本号
    if [ -z "$version1" ] || [ -z "$version2" ]; then
        return 1
    fi
    
    # 移除可能的前缀和后缀
    version1=$(echo "$version1" | sed 's/^v//' | sed 's/-.*$//')
    version2=$(echo "$version2" | sed 's/^v//' | sed 's/-.*$//')
    
    # 使用 sort -V 进行版本比较（如果支持）
    if sort --version-sort /dev/null 2>/dev/null; then
        local higher=$(printf "%s\n%s" "$version1" "$version2" | sort -V | tail -1)
        if [ "$higher" = "$version1" ]; then
            return 0  # version1 >= version2
        else
            return 1  # version1 < version2
        fi
    else
        # 备选方案：简单的数字比较
        local IFS='.'
        local v1_array=($version1)
        local v2_array=($version2)
        
        for i in {0..2}; do
            local v1_part=${v1_array[$i]:-0}
            local v2_part=${v2_array[$i]:-0}
            
            if [ "$v1_part" -gt "$v2_part" ]; then
                return 0
            elif [ "$v1_part" -lt "$v2_part" ]; then
                return 1
            fi
        done
        
        return 0  # 版本相等
    fi
}

# 更新缓存文件
#
# 功能: 更新检查缓存信息
# 参数:
#   $1 - latest_version: 最新版本号
# 返回: 无
# 复杂度: O(1) - 文件写入
update_cache() {
    local latest_version="$1"
    local today=$(date '+%Y-%m-%d')
    
    ensure_cache_dir
    cat > "$CACHE_FILE" << EOF
last_check_date=$today
latest_version=$latest_version
last_check_time=$(date '+%Y-%m-%d %H:%M:%S')
EOF
}

# 检查是否有可用更新
#
# 功能: 检查是否有新版本可用
# 参数: 无
# 返回: 0=有更新, 1=无更新或检查失败
# 复杂度: O(1) - 版本比较
is_update_available() {
    local current_version=$(get_version)
    local latest_version=$(get_latest_version)
    
    log_message "INFO" "检查更新: 当前版本=$current_version, 最新版本=$latest_version"
    
    if [ -z "$latest_version" ]; then
        log_message "WARN" "无法获取最新版本信息"
        return 1
    fi
    
    # 更新缓存
    update_cache "$latest_version"
    
    # 比较版本
    if compare_versions "$latest_version" "$current_version"; then
        log_message "INFO" "发现新版本: $latest_version"
        return 0
    else
        log_message "INFO" "当前版本已是最新"
        return 1
    fi
}

# 获取更新锁
#
# 功能: 获取更新进程锁，防止并发更新
# 参数: 无
# 返回: 0=获取成功, 1=获取失败
# 复杂度: O(1) - 文件操作
acquire_update_lock() {
    ensure_cache_dir
    
    # 检查是否已有锁文件
    if [ -f "$LOCK_FILE" ]; then
        local lock_time=$(stat -c %Y "$LOCK_FILE" 2>/dev/null || stat -f %m "$LOCK_FILE" 2>/dev/null)
        local current_time=$(date +%s)
        local lock_age=$((current_time - lock_time))
        
        # 如果锁文件超过 10 分钟，认为是僵尸锁，清理它
        if [ $lock_age -gt 600 ]; then
            log_message "WARN" "清理过期的更新锁文件"
            rm -f "$LOCK_FILE"
        else
            log_message "INFO" "更新进程已在运行，跳过"
            return 1
        fi
    fi
    
    # 创建锁文件
    echo $$ > "$LOCK_FILE"
    return 0
}

# 释放更新锁
#
# 功能: 释放更新进程锁
# 参数: 无
# 返回: 无
# 复杂度: O(1) - 文件删除
release_update_lock() {
    rm -f "$LOCK_FILE"
}

# 检测安装模式
#
# 功能: 检测当前是全局安装还是项目级安装
# 参数: 无
# 返回: "global" 或 "project"
# 复杂度: O(1) - 路径检查
detect_install_mode() {
    local script_path="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

    if [[ "$script_path" == "$HOME/.codereview-cli"* ]]; then
        echo "global"
    else
        echo "project"
    fi
}

# 获取安装目录
#
# 功能: 获取当前安装的根目录
# 参数: 无
# 返回: 安装目录路径
# 复杂度: O(1) - 路径计算
get_install_dir() {
    local install_mode=$(detect_install_mode)

    if [ "$install_mode" = "global" ]; then
        echo "$HOME/.codereview-cli"
    else
        # 项目级安装，返回项目根目录
        echo "$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    fi
}

# 创建备份
#
# 功能: 备份当前版本以便回滚
# 参数: 无
# 返回: 0=成功, 1=失败
# 复杂度: O(n) - n为文件数量
create_backup() {
    local install_dir=$(get_install_dir)
    local backup_name="backup-$(date '+%Y%m%d-%H%M%S')"
    local backup_path="$BACKUP_DIR/$backup_name"

    log_message "INFO" "创建备份: $backup_path"

    # 创建备份目录
    if ! mkdir -p "$backup_path"; then
        log_message "ERROR" "无法创建备份目录: $backup_path"
        return 1
    fi

    # 复制当前安装
    if ! cp -r "$install_dir"/* "$backup_path/" 2>/dev/null; then
        log_message "ERROR" "备份失败"
        rm -rf "$backup_path"
        return 1
    fi

    # 记录备份信息
    echo "$backup_name" > "$CACHE_DIR/last_backup"

    log_message "INFO" "备份创建成功: $backup_name"
    return 0
}

# 下载最新版本
#
# 功能: 下载最新版本到临时目录
# 参数:
#   $1 - version: 要下载的版本号
# 返回: 0=成功, 1=失败
# 复杂度: O(1) - 网络下载
download_latest_version() {
    local version="$1"
    local temp_dir="/tmp/codereview-cli-update-$$"
    local download_url="https://github.com/im47cn/codereview-cli/archive/refs/tags/v${version}.tar.gz"

    log_message "INFO" "下载版本 $version 到 $temp_dir"

    # 创建临时目录
    if ! mkdir -p "$temp_dir"; then
        log_message "ERROR" "无法创建临时目录: $temp_dir"
        return 1
    fi

    # 下载压缩包
    if ! curl -L --connect-timeout $UPDATE_TIMEOUT -o "$temp_dir/update.tar.gz" "$download_url" 2>/dev/null; then
        log_message "ERROR" "下载失败: $download_url"
        rm -rf "$temp_dir"
        return 1
    fi

    # 解压文件
    if ! tar -xzf "$temp_dir/update.tar.gz" -C "$temp_dir" --strip-components=1 2>/dev/null; then
        log_message "ERROR" "解压失败"
        rm -rf "$temp_dir"
        return 1
    fi

    # 验证下载的文件
    if [ ! -f "$temp_dir/VERSION" ] || [ ! -f "$temp_dir/install.sh" ]; then
        log_message "ERROR" "下载的文件不完整"
        rm -rf "$temp_dir"
        return 1
    fi

    echo "$temp_dir"
    return 0
}

# 安装更新
#
# 功能: 将下载的版本安装到目标目录
# 参数:
#   $1 - temp_dir: 临时下载目录
# 返回: 0=成功, 1=失败
# 复杂度: O(n) - n为文件数量
install_update() {
    local temp_dir="$1"
    local install_dir=$(get_install_dir)
    local install_mode=$(detect_install_mode)

    log_message "INFO" "安装更新到: $install_dir"

    # 检查权限
    if [ ! -w "$install_dir" ]; then
        log_message "ERROR" "没有写入权限: $install_dir"
        return 1
    fi

    # 备份当前版本
    if ! create_backup; then
        log_message "ERROR" "备份失败，取消更新"
        return 1
    fi

    # 复制新文件（排除某些文件）
    local exclude_patterns=".git .env review_logs"

    for item in "$temp_dir"/*; do
        local basename=$(basename "$item")
        local should_exclude=false

        # 检查是否应该排除
        for pattern in $exclude_patterns; do
            if [[ "$basename" == $pattern* ]]; then
                should_exclude=true
                break
            fi
        done

        if [ "$should_exclude" = false ]; then
            if ! cp -r "$item" "$install_dir/"; then
                log_message "ERROR" "复制文件失败: $basename"
                rollback_update
                return 1
            fi
        fi
    done

    # 设置执行权限
    chmod +x "$install_dir"/*.sh 2>/dev/null
    chmod +x "$install_dir"/lib/*.sh 2>/dev/null
    chmod +x "$install_dir"/githooks/* 2>/dev/null

    # 如果是全局安装，更新全局命令
    if [ "$install_mode" = "global" ]; then
        update_global_command
    fi

    log_message "INFO" "更新安装完成"
    return 0
}

# 更新全局命令
#
# 功能: 更新全局 codereview-cli 命令
# 参数: 无
# 返回: 0=成功, 1=失败
# 复杂度: O(1) - 文件操作
update_global_command() {
    local install_dir=$(get_install_dir)
    local cmd_file=""

    # 检测全局命令位置
    if [ -w "/usr/local/bin" ]; then
        cmd_file="/usr/local/bin/codereview-cli"
    elif [ -w "/usr/bin" ]; then
        cmd_file="/usr/bin/codereview-cli"
    else
        log_message "WARN" "无法更新全局命令，权限不足"
        return 1
    fi

    # 更新命令文件
    cat > "$cmd_file" << EOF
#!/bin/bash
# CodeReview CLI 全局命令 (自动更新版本)
INSTALL_DIR="$install_dir"
exec "\$INSTALL_DIR/install.sh" "\$@"
EOF

    chmod +x "$cmd_file"
    log_message "INFO" "全局命令已更新: $cmd_file"
    return 0
}

# 回滚更新
#
# 功能: 回滚到备份版本
# 参数: 无
# 返回: 0=成功, 1=失败
# 复杂度: O(n) - n为文件数量
rollback_update() {
    local install_dir=$(get_install_dir)
    local last_backup=""

    if [ -f "$CACHE_DIR/last_backup" ]; then
        last_backup=$(cat "$CACHE_DIR/last_backup")
    fi

    if [ -z "$last_backup" ] || [ ! -d "$BACKUP_DIR/$last_backup" ]; then
        log_message "ERROR" "没有可用的备份进行回滚"
        return 1
    fi

    log_message "INFO" "回滚到备份版本: $last_backup"

    # 清空当前安装目录
    rm -rf "$install_dir"/*

    # 恢复备份
    if cp -r "$BACKUP_DIR/$last_backup"/* "$install_dir/"; then
        log_message "INFO" "回滚成功"
        return 0
    else
        log_message "ERROR" "回滚失败"
        return 1
    fi
}

# 执行静默更新
#
# 功能: 执行完整的静默更新流程
# 参数: 无
# 返回: 0=成功, 1=失败
# 复杂度: O(n) - n为文件数量
perform_silent_update() {
    local latest_version=$(grep "^latest_version=" "$CACHE_FILE" 2>/dev/null | cut -d'=' -f2)

    if [ -z "$latest_version" ]; then
        log_message "ERROR" "无法获取最新版本信息"
        return 1
    fi

    log_message "INFO" "开始静默更新到版本: $latest_version"

    # 下载最新版本
    local temp_dir=$(download_latest_version "$latest_version")
    if [ $? -ne 0 ]; then
        return 1
    fi

    # 安装更新
    if install_update "$temp_dir"; then
        log_message "INFO" "静默更新成功: $latest_version"

        # 清理临时文件
        rm -rf "$temp_dir"

        # 记录更新成功
        echo "update_success=true" >> "$CACHE_FILE"
        echo "updated_version=$latest_version" >> "$CACHE_FILE"
        echo "update_time=$(date '+%Y-%m-%d %H:%M:%S')" >> "$CACHE_FILE"

        return 0
    else
        log_message "ERROR" "静默更新失败"
        rm -rf "$temp_dir"
        return 1
    fi
}

# 主要的版本检查入口函数
#
# 功能: 检查并在需要时触发更新
# 参数: 无
# 返回: 无
# 复杂度: O(1) - 条件检查
# 调用者: Git hooks, 全局命令
check_and_update_if_needed() {
    # 检查是否启用自动更新
    if ! is_auto_update_enabled; then
        return 0
    fi

    # 检查今天是否已经检查过
    if should_check_today; then
        return 0
    fi

    # 异步执行更新检查，不阻塞主进程
    (
        log_message "INFO" "开始检查更新"

        # 获取更新锁
        if ! acquire_update_lock; then
            exit 0
        fi

        # 确保释放锁
        trap 'release_update_lock' EXIT

        # 检查是否有更新
        if is_update_available; then
            log_message "INFO" "发现新版本，准备静默更新"
            perform_silent_update
        fi

    ) &

    # 不等待后台进程完成
    disown
}

# 显示更新状态
#
# 功能: 显示当前更新状态和配置
# 参数: 无
# 返回: 无
# 复杂度: O(1) - 信息显示
show_update_status() {
    local current_version=$(get_version)
    local enabled=$(is_auto_update_enabled && echo "启用" || echo "禁用")
    local install_mode=$(detect_install_mode)
    local install_dir=$(get_install_dir)

    echo -e "${BLUE}=== CodeReview CLI 自动更新状态 ===${NC}"
    echo -e "${YELLOW}当前版本:${NC} $current_version"
    echo -e "${YELLOW}安装模式:${NC} $install_mode"
    echo -e "${YELLOW}安装目录:${NC} $install_dir"
    echo -e "${YELLOW}自动更新:${NC} $enabled"

    if [ -f "$CACHE_FILE" ]; then
        echo -e "\n${BLUE}=== 更新缓存信息 ===${NC}"
        local last_check=$(grep "^last_check_date=" "$CACHE_FILE" 2>/dev/null | cut -d'=' -f2)
        local latest_version=$(grep "^latest_version=" "$CACHE_FILE" 2>/dev/null | cut -d'=' -f2)
        local update_success=$(grep "^update_success=" "$CACHE_FILE" 2>/dev/null | cut -d'=' -f2)

        if [ ! -z "$last_check" ]; then
            echo -e "${YELLOW}最后检查:${NC} $last_check"
        fi
        if [ ! -z "$latest_version" ]; then
            echo -e "${YELLOW}最新版本:${NC} $latest_version"
        fi
        if [ "$update_success" = "true" ]; then
            local updated_version=$(grep "^updated_version=" "$CACHE_FILE" 2>/dev/null | cut -d'=' -f2)
            local update_time=$(grep "^update_time=" "$CACHE_FILE" 2>/dev/null | cut -d'=' -f2)
            echo -e "${GREEN}✓ 最近更新:${NC} $updated_version ($update_time)"
        fi
    fi

    # 检查是否有待显示的更新提示
    check_and_show_update_notification
}

# 检查并显示更新通知
#
# 功能: 检查是否有成功的更新需要通知用户
# 参数: 无
# 返回: 无
# 复杂度: O(1) - 文件检查
check_and_show_update_notification() {
    if [ ! -f "$CACHE_FILE" ]; then
        return 0
    fi

    local update_success=$(grep "^update_success=" "$CACHE_FILE" 2>/dev/null | cut -d'=' -f2)
    local notification_shown=$(grep "^notification_shown=" "$CACHE_FILE" 2>/dev/null | cut -d'=' -f2)

    if [ "$update_success" = "true" ] && [ "$notification_shown" != "true" ]; then
        local updated_version=$(grep "^updated_version=" "$CACHE_FILE" 2>/dev/null | cut -d'=' -f2)

        echo -e "${GREEN}🎉 CodeReview CLI 已自动更新到版本 $updated_version${NC}"
        echo -e "${BLUE}💡 运行 'codereview-cli version' 查看详细信息${NC}"

        # 标记通知已显示
        echo "notification_shown=true" >> "$CACHE_FILE"
    fi
}

# 手动检查更新
#
# 功能: 手动触发更新检查
# 参数: 无
# 返回: 0=成功, 1=失败
# 复杂度: O(1) - 强制检查
manual_check_update() {
    echo -e "${BLUE}🔍 手动检查更新...${NC}"

    # 清除今天的检查缓存，强制检查
    if [ -f "$CACHE_FILE" ]; then
        sed -i.bak '/^last_check_date=/d' "$CACHE_FILE" 2>/dev/null || true
    fi

    # 获取更新锁
    if ! acquire_update_lock; then
        echo -e "${YELLOW}⚠️  更新检查正在进行中，请稍后再试${NC}"
        return 1
    fi

    # 确保释放锁
    trap 'release_update_lock' EXIT

    log_message "INFO" "手动检查更新"

    # 检查是否有更新
    if is_update_available; then
        local latest_version=$(grep "^latest_version=" "$CACHE_FILE" 2>/dev/null | cut -d'=' -f2)
        echo -e "${GREEN}✅ 发现新版本: $latest_version${NC}"

        if is_auto_update_enabled; then
            echo -e "${BLUE}🚀 开始自动更新...${NC}"
            if perform_silent_update; then
                echo -e "${GREEN}✅ 更新成功！${NC}"
                return 0
            else
                echo -e "${RED}❌ 自动更新失败，请查看日志: $LOG_FILE${NC}"
                return 1
            fi
        else
            echo -e "${YELLOW}💡 自动更新已禁用，请手动更新：${NC}"
            echo -e "${BLUE}   codereview-cli update${NC}"
            return 0
        fi
    else
        echo -e "${GREEN}✅ 当前版本已是最新${NC}"
        return 0
    fi
}

# 配置自动更新
#
# 功能: 交互式配置自动更新选项
# 参数: 无
# 返回: 无
# 复杂度: O(1) - 用户交互
configure_auto_update() {
    echo -e "${BLUE}=== 配置自动更新 ===${NC}"
    echo ""

    local current_enabled=$(is_auto_update_enabled && echo "启用" || echo "禁用")
    echo -e "${YELLOW}当前状态:${NC} $current_enabled"
    echo ""

    echo "请选择自动更新设置："
    echo "1) 启用自动更新 (推荐)"
    echo "2) 禁用自动更新"
    echo "3) 取消"
    echo ""

    read -p "请输入选择 [1-3]: " choice

    case "$choice" in
        1)
            set_config_value "AUTO_UPDATE_ENABLED" "true" "global"
            echo -e "${GREEN}✅ 自动更新已启用${NC}"
            ;;
        2)
            set_config_value "AUTO_UPDATE_ENABLED" "false" "global"
            echo -e "${YELLOW}⚠️  自动更新已禁用${NC}"
            echo -e "${BLUE}💡 您仍可以使用 'codereview-cli update' 手动更新${NC}"
            ;;
        3)
            echo -e "${BLUE}取消配置${NC}"
            return 0
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            return 1
            ;;
    esac
}

# 清理更新缓存
#
# 功能: 清理更新相关的缓存文件
# 参数: 无
# 返回: 无
# 复杂度: O(1) - 文件删除
clean_update_cache() {
    echo -e "${BLUE}🧹 清理更新缓存...${NC}"

    local files_cleaned=0

    if [ -f "$CACHE_FILE" ]; then
        rm -f "$CACHE_FILE"
        files_cleaned=$((files_cleaned + 1))
    fi

    if [ -f "$LOCK_FILE" ]; then
        rm -f "$LOCK_FILE"
        files_cleaned=$((files_cleaned + 1))
    fi

    if [ -d "$BACKUP_DIR" ]; then
        rm -rf "$BACKUP_DIR"
        files_cleaned=$((files_cleaned + 1))
    fi

    echo -e "${GREEN}✅ 已清理 $files_cleaned 个缓存文件${NC}"
}

# 主函数
#
# 功能: 命令行接口
# 参数: $@ - 命令行参数
# 返回: 0=成功, 1=失败
# 复杂度: O(1) - 命令分发
main() {
    case "${1:-status}" in
        "check")
            manual_check_update
            ;;
        "status")
            show_update_status
            ;;
        "configure"|"config")
            configure_auto_update
            ;;
        "clean")
            clean_update_cache
            ;;
        "enable")
            set_config_value "AUTO_UPDATE_ENABLED" "true" "global"
            echo -e "${GREEN}✅ 自动更新已启用${NC}"
            ;;
        "disable")
            set_config_value "AUTO_UPDATE_ENABLED" "false" "global"
            echo -e "${YELLOW}⚠️  自动更新已禁用${NC}"
            ;;
        "help"|"-h"|"--help")
            echo "CodeReview CLI 自动更新管理工具"
            echo ""
            echo "用法: $0 <命令>"
            echo ""
            echo "命令:"
            echo "  check      - 手动检查并安装更新"
            echo "  status     - 显示更新状态"
            echo "  configure  - 配置自动更新选项"
            echo "  enable     - 启用自动更新"
            echo "  disable    - 禁用自动更新"
            echo "  clean      - 清理更新缓存"
            echo "  help       - 显示帮助信息"
            echo ""
            echo "自动更新功能："
            echo "- 每天首次使用时自动检查更新"
            echo "- 静默下载和安装，不中断工作流"
            echo "- 自动备份和回滚机制"
            echo "- 可通过配置禁用"
            ;;
        *)
            echo -e "${RED}❌ 未知命令: $1${NC}"
            echo "运行 '$0 help' 查看可用命令"
            return 1
            ;;
    esac
}

# 如果直接执行此脚本，运行主函数
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
